import * as THREE from 'three';

export class Player {
    constructor(scene) {
        this.scene = scene;
        this.position = new THREE.Vector3(0, 5, 0);
        this.velocity = new THREE.Vector3();
        this.direction = new THREE.Vector3();

        // Movement properties
        this.walkSpeed = 15;
        this.runSpeed = 25;
        this.jumpForce = 12;
        this.gravity = -30;
        this.isGrounded = false;
        this.height = 1.8;

        // Input state
        this.keys = {
            forward: false,
            backward: false,
            left: false,
            right: false,
            run: false,
            jump: false
        };

        // Collision detection
        this.raycaster = new THREE.Raycaster();
        this.collisionObjects = [];

        // Camera reference for movement direction
        this.cameraController = null;

        this.setupInputHandlers();
    }

    init() {
        // Create a simple player representation (invisible capsule for collision)
        const geometry = new THREE.CapsuleGeometry(0.5, this.height, 4, 8);
        const material = new THREE.MeshBasicMaterial({ 
            color: 0xff0000, 
            transparent: true, 
            opacity: 0 // Invisible
        });
        this.mesh = new THREE.Mesh(geometry, material);
        this.mesh.position.copy(this.position);
        this.scene.add(this.mesh);
        
        // Find ground level
        this.findGroundLevel();
    }

    setupInputHandlers() {
        document.addEventListener('keydown', (event) => {
            switch (event.code) {
                case 'KeyW':
                case 'ArrowUp':
                    this.keys.forward = true;
                    break;
                case 'KeyS':
                case 'ArrowDown':
                    this.keys.backward = true;
                    break;
                case 'KeyA':
                case 'ArrowLeft':
                    this.keys.left = true;
                    break;
                case 'KeyD':
                case 'ArrowRight':
                    this.keys.right = true;
                    break;
                case 'ShiftLeft':
                case 'ShiftRight':
                    this.keys.run = true;
                    break;
                case 'Space':
                    this.keys.jump = true;
                    event.preventDefault();
                    break;
            }
        });

        document.addEventListener('keyup', (event) => {
            switch (event.code) {
                case 'KeyW':
                case 'ArrowUp':
                    this.keys.forward = false;
                    break;
                case 'KeyS':
                case 'ArrowDown':
                    this.keys.backward = false;
                    break;
                case 'KeyA':
                case 'ArrowLeft':
                    this.keys.left = false;
                    break;
                case 'KeyD':
                case 'ArrowRight':
                    this.keys.right = false;
                    break;
                case 'ShiftLeft':
                case 'ShiftRight':
                    this.keys.run = false;
                    break;
                case 'Space':
                    this.keys.jump = false;
                    break;
            }
        });
    }

    findGroundLevel() {
        // Cast ray downward to find ground
        this.raycaster.set(this.position, new THREE.Vector3(0, -1, 0));
        const intersects = this.raycaster.intersectObjects(this.collisionObjects, true);
        
        if (intersects.length > 0) {
            this.position.y = intersects[0].point.y + this.height / 2;
            this.isGrounded = true;
        } else {
            // Default ground level
            this.position.y = 5;
            this.isGrounded = true;
        }
        
        if (this.mesh) {
            this.mesh.position.copy(this.position);
        }
    }

    checkGroundCollision() {
        // Cast ray downward from player position
        const rayOrigin = this.position.clone();
        rayOrigin.y += 0.1; // Slight offset to avoid self-intersection
        
        this.raycaster.set(rayOrigin, new THREE.Vector3(0, -1, 0));
        const intersects = this.raycaster.intersectObjects(this.collisionObjects, true);
        
        if (intersects.length > 0) {
            const groundY = intersects[0].point.y;
            const playerBottom = this.position.y - this.height / 2;
            
            if (playerBottom <= groundY + 0.1) {
                this.position.y = groundY + this.height / 2;
                this.velocity.y = 0;
                this.isGrounded = true;
                return true;
            }
        }
        
        this.isGrounded = false;
        return false;
    }

    checkWallCollision(direction) {
        // Cast rays horizontally to check for walls
        const rayOrigin = this.position.clone();
        const rayDirection = direction.clone().normalize();
        
        this.raycaster.set(rayOrigin, rayDirection);
        const intersects = this.raycaster.intersectObjects(this.collisionObjects, true);
        
        if (intersects.length > 0 && intersects[0].distance < 1.0) {
            return true;
        }
        
        return false;
    }

    update(deltaTime) {
        if (!this.mesh) return;

        // Calculate movement direction relative to camera
        this.direction.set(0, 0, 0);

        if (this.cameraController) {
            const forward = this.cameraController.getForwardDirection();
            const right = this.cameraController.getRightDirection();

            if (this.keys.forward) this.direction.add(forward);
            if (this.keys.backward) this.direction.sub(forward);
            if (this.keys.left) this.direction.sub(right);
            if (this.keys.right) this.direction.add(right);
        } else {
            // Fallback to world directions if no camera controller
            if (this.keys.forward) this.direction.z -= 1;
            if (this.keys.backward) this.direction.z += 1;
            if (this.keys.left) this.direction.x -= 1;
            if (this.keys.right) this.direction.x += 1;
        }

        // Normalize direction to prevent faster diagonal movement
        if (this.direction.length() > 0) {
            this.direction.normalize();
        }

        // Apply speed
        const currentSpeed = this.keys.run ? this.runSpeed : this.walkSpeed;
        const moveVector = this.direction.clone().multiplyScalar(currentSpeed * deltaTime);
        
        // Check wall collisions before moving
        if (moveVector.length() > 0) {
            if (!this.checkWallCollision(moveVector)) {
                this.position.x += moveVector.x;
                this.position.z += moveVector.z;
            }
        }
        
        // Handle jumping
        if (this.keys.jump && this.isGrounded) {
            this.velocity.y = this.jumpForce;
            this.isGrounded = false;
        }
        
        // Apply gravity
        if (!this.isGrounded) {
            this.velocity.y += this.gravity * deltaTime;
            this.position.y += this.velocity.y * deltaTime;
        }
        
        // Check ground collision
        this.checkGroundCollision();
        
        // Prevent falling through world
        if (this.position.y < -50) {
            this.position.set(0, 10, 0);
            this.velocity.set(0, 0, 0);
        }
        
        // Update mesh position
        this.mesh.position.copy(this.position);
    }

    addCollisionObject(object) {
        this.collisionObjects.push(object);
    }

    removeCollisionObject(object) {
        const index = this.collisionObjects.indexOf(object);
        if (index > -1) {
            this.collisionObjects.splice(index, 1);
        }
    }

    getPosition() {
        return this.position.clone();
    }

    setPosition(x, y, z) {
        this.position.set(x, y, z);
        if (this.mesh) {
            this.mesh.position.copy(this.position);
        }
    }

    setCameraController(cameraController) {
        this.cameraController = cameraController;
    }
}
