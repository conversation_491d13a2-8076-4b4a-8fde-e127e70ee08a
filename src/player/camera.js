import * as THREE from 'three';

export class CameraController {
    constructor(camera, player) {
        this.camera = camera;
        this.player = player;
        
        // Camera settings
        this.mouseSensitivity = 0.002;
        this.distance = 8; // Distance behind player for third-person
        this.height = 3; // Height above player
        this.minDistance = 2;
        this.maxDistance = 20;
        
        // Rotation
        this.phi = 0; // Horizontal rotation
        this.theta = Math.PI / 6; // Vertical rotation (slight downward angle)
        this.minTheta = 0.1;
        this.maxTheta = Math.PI - 0.1;
        
        // Smooth camera movement
        this.targetPosition = new THREE.Vector3();
        this.currentPosition = new THREE.Vector3();
        this.lookAtTarget = new THREE.Vector3();
        this.smoothFactor = 0.1;
        
        // Mouse state
        this.isPointerLocked = false;
        
        this.setupMouseControls();
        this.setupScrollControls();
    }

    setupMouseControls() {
        document.addEventListener('pointerlockchange', () => {
            this.isPointerLocked = document.pointerLockElement !== null;
        });

        document.addEventListener('mousemove', (event) => {
            if (!this.isPointerLocked) return;

            const movementX = event.movementX || 0;
            const movementY = event.movementY || 0;

            // Update rotation based on mouse movement
            this.phi -= movementX * this.mouseSensitivity;
            this.theta += movementY * this.mouseSensitivity;

            // Clamp vertical rotation
            this.theta = Math.max(this.minTheta, Math.min(this.maxTheta, this.theta));
        });
    }

    setupScrollControls() {
        document.addEventListener('wheel', (event) => {
            if (!this.isPointerLocked) return;
            
            event.preventDefault();
            
            // Zoom in/out
            const zoomSpeed = 0.5;
            this.distance += event.deltaY * 0.01 * zoomSpeed;
            this.distance = Math.max(this.minDistance, Math.min(this.maxDistance, this.distance));
        });
    }

    calculateCameraPosition() {
        const playerPos = this.player.getPosition();
        
        // Calculate camera position in spherical coordinates
        const x = playerPos.x + this.distance * Math.sin(this.theta) * Math.cos(this.phi);
        const y = playerPos.y + this.height + this.distance * Math.cos(this.theta);
        const z = playerPos.z + this.distance * Math.sin(this.theta) * Math.sin(this.phi);
        
        this.targetPosition.set(x, y, z);
        
        // Look at target (slightly above player)
        this.lookAtTarget.copy(playerPos);
        this.lookAtTarget.y += this.height * 0.5;
    }

    checkCameraCollision() {
        // Cast ray from player to camera to check for obstacles
        const playerPos = this.player.getPosition();
        const direction = this.targetPosition.clone().sub(playerPos).normalize();
        const maxDistance = this.distance;
        
        const raycaster = new THREE.Raycaster();
        raycaster.set(playerPos, direction);
        
        // Get collision objects from player
        const intersects = raycaster.intersectObjects(this.player.collisionObjects, true);
        
        if (intersects.length > 0) {
            const hitDistance = intersects[0].distance;
            if (hitDistance < maxDistance) {
                // Move camera closer to avoid clipping through walls
                const safeDistance = Math.max(hitDistance - 0.5, this.minDistance);
                const adjustedPos = playerPos.clone().add(direction.multiplyScalar(safeDistance));
                this.targetPosition.copy(adjustedPos);
            }
        }
    }

    update() {
        if (!this.player) return;
        
        // Calculate desired camera position
        this.calculateCameraPosition();
        
        // Check for collisions and adjust if necessary
        this.checkCameraCollision();
        
        // Smooth camera movement
        this.currentPosition.lerp(this.targetPosition, this.smoothFactor);
        this.camera.position.copy(this.currentPosition);
        
        // Look at the player
        this.camera.lookAt(this.lookAtTarget);
        
        // Update player direction based on camera rotation (for movement relative to camera)
        this.updatePlayerDirection();
    }

    updatePlayerDirection() {
        // Get camera's forward direction (projected onto horizontal plane)
        const cameraDirection = new THREE.Vector3();
        this.camera.getWorldDirection(cameraDirection);
        cameraDirection.y = 0; // Remove vertical component
        cameraDirection.normalize();
        
        // Calculate right direction
        const rightDirection = new THREE.Vector3();
        rightDirection.crossVectors(cameraDirection, new THREE.Vector3(0, 1, 0));
        rightDirection.normalize();
        
        // Store directions for player movement
        this.forwardDirection = cameraDirection;
        this.rightDirection = rightDirection;
    }

    getForwardDirection() {
        return this.forwardDirection || new THREE.Vector3(0, 0, -1);
    }

    getRightDirection() {
        return this.rightDirection || new THREE.Vector3(1, 0, 0);
    }

    // Switch between first-person and third-person views
    toggleView() {
        if (this.distance <= this.minDistance) {
            // Switch to third-person
            this.distance = 8;
        } else {
            // Switch to first-person
            this.distance = 0.1;
        }
    }

    // Reset camera to default position
    reset() {
        this.phi = 0;
        this.theta = Math.PI / 6;
        this.distance = 8;
    }

    // Get camera information for other systems
    getCameraInfo() {
        return {
            position: this.camera.position.clone(),
            direction: this.getForwardDirection(),
            phi: this.phi,
            theta: this.theta,
            distance: this.distance
        };
    }
}
