import * as THREE from 'three';
import { Player } from './player/player.js';
import { CameraController } from './player/camera.js';
import { TerrainManager } from './world/terrain.js';
import { StructureManager } from './world/structures.js';
import { OptimizationManager } from './utils/optimization.js';

class MedievalGame {
    constructor() {
        this.scene = null;
        this.camera = null;
        this.renderer = null;
        this.player = null;
        this.cameraController = null;
        this.terrainManager = null;
        this.structureManager = null;
        this.optimizationManager = null;
        
        this.clock = new THREE.Clock();
        this.isLoaded = false;
        this.loadingProgress = 0;
        
        // Performance tracking
        this.frameCount = 0;
        this.lastTime = performance.now();
        this.fps = 0;
    }

    async init() {
        this.setupRenderer();
        this.setupScene();
        this.setupCamera();
        this.setupLighting();
        
        // Initialize managers
        this.optimizationManager = new OptimizationManager(this.renderer);
        this.terrainManager = new TerrainManager(this.scene);
        this.structureManager = new StructureManager(this.scene);
        this.player = new Player(this.scene);
        this.cameraController = new CameraController(this.camera, this.player);

        // Connect player and camera
        this.player.setCameraController(this.cameraController);

        // Load world
        await this.loadWorld();
        
        this.setupEventListeners();
        this.hideLoadingScreen();
        this.startGameLoop();
    }

    setupRenderer() {
        const canvas = document.getElementById('game-canvas');
        this.renderer = new THREE.WebGLRenderer({ 
            canvas: canvas,
            antialias: true,
            powerPreference: "high-performance"
        });
        
        this.renderer.setSize(window.innerWidth, window.innerHeight);
        this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
        this.renderer.shadowMap.enabled = true;
        this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;
        this.renderer.outputColorSpace = THREE.SRGBColorSpace;
        this.renderer.toneMapping = THREE.ACESFilmicToneMapping;
        this.renderer.toneMappingExposure = 1.2;
        
        // Enable fog
        this.renderer.setClearColor(0x87CEEB, 1);
    }

    setupScene() {
        this.scene = new THREE.Scene();
        this.scene.fog = new THREE.Fog(0x87CEEB, 50, 500);
    }

    setupCamera() {
        this.camera = new THREE.PerspectiveCamera(
            75,
            window.innerWidth / window.innerHeight,
            0.1,
            1000
        );
        this.camera.position.set(0, 10, 20);
    }

    setupLighting() {
        // Ambient light
        const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
        this.scene.add(ambientLight);

        // Directional light (sun)
        const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
        directionalLight.position.set(100, 100, 50);
        directionalLight.castShadow = true;
        directionalLight.shadow.mapSize.width = 2048;
        directionalLight.shadow.mapSize.height = 2048;
        directionalLight.shadow.camera.near = 0.5;
        directionalLight.shadow.camera.far = 500;
        directionalLight.shadow.camera.left = -100;
        directionalLight.shadow.camera.right = 100;
        directionalLight.shadow.camera.top = 100;
        directionalLight.shadow.camera.bottom = -100;
        this.scene.add(directionalLight);

        // Sky gradient
        const skyGeometry = new THREE.SphereGeometry(800, 32, 32);
        const skyMaterial = new THREE.ShaderMaterial({
            uniforms: {
                topColor: { value: new THREE.Color(0x0077ff) },
                bottomColor: { value: new THREE.Color(0xffffff) },
                offset: { value: 33 },
                exponent: { value: 0.6 }
            },
            vertexShader: `
                varying vec3 vWorldPosition;
                void main() {
                    vec4 worldPosition = modelMatrix * vec4(position, 1.0);
                    vWorldPosition = worldPosition.xyz;
                    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
                }
            `,
            fragmentShader: `
                uniform vec3 topColor;
                uniform vec3 bottomColor;
                uniform float offset;
                uniform float exponent;
                varying vec3 vWorldPosition;
                void main() {
                    float h = normalize(vWorldPosition + offset).y;
                    gl_FragColor = vec4(mix(bottomColor, topColor, max(pow(max(h, 0.0), exponent), 0.0)), 1.0);
                }
            `,
            side: THREE.BackSide
        });
        
        const sky = new THREE.Mesh(skyGeometry, skyMaterial);
        this.scene.add(sky);
    }

    async loadWorld() {
        this.updateLoadingProgress(10, "Generating terrain...");
        const terrain = await this.terrainManager.generateTerrain();

        this.updateLoadingProgress(50, "Building medieval structures...");
        const structures = await this.structureManager.createStructures();

        this.updateLoadingProgress(80, "Initializing player...");
        this.player.init();

        // Add collision objects
        if (terrain) {
            this.player.addCollisionObject(terrain);
        }
        structures.forEach(structure => {
            this.player.addCollisionObject(structure);
        });

        this.updateLoadingProgress(100, "Ready!");
        await new Promise(resolve => setTimeout(resolve, 500));
    }

    updateLoadingProgress(progress, message) {
        this.loadingProgress = progress;
        const progressBar = document.querySelector('.loading-progress');
        const loadingText = document.querySelector('.loading-content h2');
        
        if (progressBar) progressBar.style.width = `${progress}%`;
        if (loadingText) loadingText.textContent = message;
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        loadingScreen.classList.add('hidden');
        this.isLoaded = true;
    }

    setupEventListeners() {
        window.addEventListener('resize', () => this.onWindowResize());
        
        // Pointer lock for mouse look
        const canvas = this.renderer.domElement;
        canvas.addEventListener('click', () => {
            canvas.requestPointerLock();
        });
    }

    onWindowResize() {
        this.camera.aspect = window.innerWidth / window.innerHeight;
        this.camera.updateProjectionMatrix();
        this.renderer.setSize(window.innerWidth, window.innerHeight);
    }

    updatePerformanceStats() {
        this.frameCount++;
        const currentTime = performance.now();
        
        if (currentTime - this.lastTime >= 1000) {
            this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime));
            this.frameCount = 0;
            this.lastTime = currentTime;
            
            // Update UI
            const fpsCounter = document.getElementById('fps-counter');
            const trianglesCounter = document.getElementById('triangles-counter');
            
            if (fpsCounter) fpsCounter.textContent = `FPS: ${this.fps}`;
            if (trianglesCounter) {
                const triangles = this.renderer.info.render.triangles;
                trianglesCounter.textContent = `Triangles: ${triangles.toLocaleString()}`;
            }
        }
    }

    startGameLoop() {
        const animate = () => {
            requestAnimationFrame(animate);
            
            if (!this.isLoaded) return;
            
            const deltaTime = this.clock.getDelta();
            
            // Update game systems
            this.player.update(deltaTime);
            this.cameraController.update(deltaTime);
            this.optimizationManager.update(this.camera);
            
            // Render
            this.renderer.render(this.scene, this.camera);
            
            // Update performance stats
            this.updatePerformanceStats();
        };
        
        animate();
    }
}

// Initialize game when page loads
window.addEventListener('DOMContentLoaded', () => {
    const game = new MedievalGame();
    game.init().catch(console.error);
});
